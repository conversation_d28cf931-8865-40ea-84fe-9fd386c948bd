import { Component, OnInit } from '@angular/core';
import {Infordb} from "infordb";

@Component({
  selector: 'app-users',
  templateUrl: './users.page.html',
  styleUrls: ['./users.page.scss'],
  providers: [Infordb]
})
export class UsersPage implements OnInit {

  public loading = false;
  public users = [];

  constructor(
      private infordb: Infordb
  ) { }


  async ngOnInit() {
    await this.initUsers();
  }

  async initUsers(){
    this.loading = false;

    const { data } = await this.infordb.post('https://test.ikbentessa.app/api/users/get')
    this.users = data.users;

    console.log(data);
  }

}
