import { Component, OnInit } from '@angular/core';
import { AlertController, ToastController } from '@ionic/angular';
import { Infordb } from "infordb";

@Component({
  selector: 'app-users',
  templateUrl: './users.page.html',
  styleUrls: ['./users.page.scss'],
  providers: [Infordb]
})
export class UsersPage implements OnInit {

  public loading = false;
  public users = [];
  public connectingUserId: number | null = null;

  constructor(
    private infordb: Infordb,
    private alertController: AlertController,
    private toastController: ToastController
  ) { }

  async ngOnInit() {
    await this.initUsers();
  }

  async initUsers() {
    this.loading = true;

    try {
      const { data } = await this.infordb.post('https://test.ikbentessa.app/api/users/get');
      this.users = data.users;
      console.log('Users loaded:', data);
    } catch (error) {
      console.error('Error loading users:', error);
      await this.showToast('Error loading users. Please try again.', 'danger');
    } finally {
      this.loading = false;
    }
  }

  async connectToNFC(user: any) {
    this.connectingUserId = user.id;

    try {
      // Show confirmation dialog
      const alert = await this.alertController.create({
        header: 'Connect to NFC Tag',
        message: `Do you want to connect ${user.name} ${user.lastname} to an NFC tag?`,
        buttons: [
          {
            text: 'Cancel',
            role: 'cancel',
            handler: () => {
              this.connectingUserId = null;
            }
          },
          {
            text: 'Connect',
            handler: async () => {
              await this.performNFCConnection(user);
            }
          }
        ]
      });

      await alert.present();
    } catch (error) {
      console.error('Error showing NFC connection dialog:', error);
      this.connectingUserId = null;
    }
  }

  private async performNFCConnection(user: any) {
    try {
      // Simulate NFC connection process
      // In a real implementation, you would use an NFC plugin like @ionic-native/nfc
      await this.simulateNFCWrite(user);

      await this.showToast(`Successfully connected ${user.name} ${user.lastname} to NFC tag!`, 'success');
    } catch (error) {
      console.error('Error connecting to NFC:', error);
      await this.showToast('Failed to connect to NFC tag. Please try again.', 'danger');
    } finally {
      this.connectingUserId = null;
    }
  }

  private async simulateNFCWrite(user: any): Promise<void> {
    return new Promise((resolve, reject) => {
      // Simulate NFC writing process
      setTimeout(() => {
        // Create user data to write to NFC tag
        const nfcData = {
          userId: user.id,
          name: user.name,
          lastname: user.lastname,
          email: user.email,
          pnumber: user.pnumber,
          role: user.role?.name || 'No role',
          timestamp: new Date().toISOString()
        };

        console.log('NFC Data to write:', nfcData);

        // In a real implementation, you would write this data to the NFC tag
        // For now, we'll just simulate success
        const success = Math.random() > 0.1; // 90% success rate for simulation

        if (success) {
          resolve();
        } else {
          reject(new Error('NFC write failed'));
        }
      }, 2000); // Simulate 2 second NFC operation
    });
  }

  private async showToast(message: string, color: 'success' | 'danger' | 'warning' = 'success') {
    const toast = await this.toastController.create({
      message: message,
      duration: 3000,
      position: 'bottom',
      color: color,
      buttons: [
        {
          text: 'Dismiss',
          role: 'cancel'
        }
      ]
    });

    await toast.present();
  }
}
