.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;

  ion-spinner {
    margin-bottom: 1rem;
  }

  p {
    color: var(--ion-color-medium);
    font-size: 1rem;
  }
}

.users-container {
  padding: 1rem;
}

.user-card {
  margin-bottom: 1rem;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  ion-card-header {
    padding-bottom: 0.5rem;

    ion-card-title {
      font-size: 1.2rem;
      font-weight: 600;
      color: var(--ion-color-primary);
    }

    ion-card-subtitle {
      font-size: 0.9rem;
      color: var(--ion-color-medium);
      margin-top: 0.25rem;
    }
  }

  ion-card-content {
    padding-top: 0.5rem;
  }
}

.user-info {
  margin-bottom: 1rem;

  ion-item {
    --padding-start: 0;
    --inner-padding-end: 0;

    ion-icon {
      color: var(--ion-color-medium);
      margin-right: 0.5rem;
    }

    ion-label {
      font-size: 0.9rem;
    }
  }
}

.action-buttons {
  margin-top: 1rem;

  ion-button {
    --border-radius: 8px;
    height: 44px;
    font-weight: 500;

    &[disabled] {
      opacity: 0.6;
    }
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 2rem;
  text-align: center;
  min-height: 50vh;

  ion-icon {
    color: var(--ion-color-medium);
    margin-bottom: 1rem;
  }

  h2 {
    color: var(--ion-color-dark);
    margin-bottom: 0.5rem;
    font-size: 1.5rem;
  }

  p {
    color: var(--ion-color-medium);
    margin-bottom: 2rem;
    font-size: 1rem;
    line-height: 1.5;
  }

  ion-button {
    --border-radius: 8px;
  }
}

ion-fab {
  ion-fab-button {
    --background: var(--ion-color-primary);
    --color: white;
    --box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);

    &[disabled] {
      --background: var(--ion-color-medium);
      opacity: 0.6;
    }
  }
}

// Responsive design
@media (min-width: 768px) {
  .users-container {
    padding: 2rem;
    max-width: 800px;
    margin: 0 auto;
  }

  .user-card {
    margin-bottom: 1.5rem;
  }
}

// Dark mode support
@media (prefers-color-scheme: dark) {
  .user-card {
    box-shadow: 0 2px 8px rgba(255, 255, 255, 0.1);
  }
}
