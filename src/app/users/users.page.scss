.search-container {
  padding: 1rem;
  padding-bottom: 0;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;

  ion-spinner {
    margin-bottom: 1rem;
  }

  p {
    color: var(--ion-color-medium);
    font-size: 1rem;
  }
}

.users-container {
  padding: 0;
}

.user-item {
  --padding-start: 1rem;
  --padding-end: 1rem;
  --min-height: 60px;
  --background: transparent;
  border-bottom: 1px solid var(--ion-color-light);

  &:last-child {
    border-bottom: none;
  }

  .user-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    min-width: 0; // Allow text to truncate

    .user-name {
      font-size: 1rem;
      font-weight: 600;
      color: var(--ion-color-dark);
      margin-bottom: 0.25rem;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .user-details {
      display: flex;
      flex-direction: column;
      gap: 0.125rem;

      .user-role {
        font-size: 0.8rem;
        color: var(--ion-color-primary);
        font-weight: 500;
      }

      .user-email {
        font-size: 0.75rem;
        color: var(--ion-color-medium);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .user-phone {
        font-size: 0.75rem;
        color: var(--ion-color-medium);
      }
    }
  }

  ion-button {
    --border-radius: 6px;
    --padding-start: 0.75rem;
    --padding-end: 0.75rem;
    height: 32px;
    margin: 0;

    &[disabled] {
      opacity: 0.6;
    }
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 2rem;
  text-align: center;
  min-height: 50vh;

  ion-icon {
    color: var(--ion-color-medium);
    margin-bottom: 1rem;
  }

  h2 {
    color: var(--ion-color-dark);
    margin-bottom: 0.5rem;
    font-size: 1.5rem;
  }

  p {
    color: var(--ion-color-medium);
    margin-bottom: 2rem;
    font-size: 1rem;
    line-height: 1.5;
  }

  ion-button {
    --border-radius: 8px;
  }
}

ion-fab {
  ion-fab-button {
    --background: var(--ion-color-primary);
    --color: white;
    --box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);

    &[disabled] {
      --background: var(--ion-color-medium);
      opacity: 0.6;
    }
  }
}

// Responsive design
@media (min-width: 768px) {
  .search-container {
    padding: 2rem;
    padding-bottom: 1rem;
    max-width: 800px;
    margin: 0 auto;
  }

  .users-container {
    max-width: 800px;
    margin: 0 auto;
  }

  .user-item {
    --padding-start: 2rem;
    --padding-end: 2rem;
    --min-height: 70px;

    .user-info {
      .user-name {
        font-size: 1.1rem;
      }

      .user-details {
        flex-direction: row;
        gap: 1rem;
        align-items: center;

        .user-email,
        .user-phone {
          font-size: 0.85rem;
        }
      }
    }
  }
}

// Dark mode support
@media (prefers-color-scheme: dark) {
  .user-item {
    border-bottom-color: var(--ion-color-dark);
  }
}
