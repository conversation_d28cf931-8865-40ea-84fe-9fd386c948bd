<ion-header>
    <ion-toolbar>
        <ion-buttons slot="start">
            <ion-back-button text="Terug" defaultHref="/home"></ion-back-button>
        </ion-buttons>
        <ion-title>Users</ion-title>
    </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <ion-header collapse="condense">
    <ion-toolbar>
      <ion-title size="large">Users Overview</ion-title>
    </ion-toolbar>
  </ion-header>

  <!-- Loading spinner -->
  <div *ngIf="loading" class="loading-container">
    <ion-spinner name="crescent"></ion-spinner>
    <p>Loading users...</p>
  </div>

  <!-- Users list -->
  <div *ngIf="!loading && users.length > 0" class="users-container">
    <ion-card *ngFor="let user of users" class="user-card">
      <ion-card-header>
        <ion-card-title>{{user.name}} {{user.lastname}}</ion-card-title>
        <ion-card-subtitle>{{user.role?.name || 'No role assigned'}}</ion-card-subtitle>
      </ion-card-header>

      <ion-card-content>
        <div class="user-info">
          <ion-item lines="none">
            <ion-icon name="mail-outline" slot="start"></ion-icon>
            <ion-label>{{user.email}}</ion-label>
          </ion-item>

          <ion-item lines="none" *ngIf="user.pnumber">
            <ion-icon name="call-outline" slot="start"></ion-icon>
            <ion-label>{{user.pnumber}}</ion-label>
          </ion-item>
        </div>

        <div class="action-buttons">
          <ion-button
            expand="block"
            fill="outline"
            color="primary"
            (click)="connectToNFC(user)"
            [disabled]="connectingUserId === user.id">
            <ion-icon name="wifi-outline" slot="start"></ion-icon>
            <span *ngIf="connectingUserId !== user.id">Connect to NFC Tag</span>
            <span *ngIf="connectingUserId === user.id">Connecting...</span>
          </ion-button>
        </div>
      </ion-card-content>
    </ion-card>
  </div>

  <!-- Empty state -->
  <div *ngIf="!loading && users.length === 0" class="empty-state">
    <ion-icon name="people-outline" size="large"></ion-icon>
    <h2>No Users Found</h2>
    <p>There are currently no users to display.</p>
    <ion-button fill="outline" (click)="initUsers()">
      <ion-icon name="refresh-outline" slot="start"></ion-icon>
      Refresh
    </ion-button>
  </div>

  <!-- Refresh button -->
  <ion-fab vertical="bottom" horizontal="end" slot="fixed">
    <ion-fab-button (click)="initUsers()" [disabled]="loading">
      <ion-icon name="refresh-outline"></ion-icon>
    </ion-fab-button>
  </ion-fab>
</ion-content>
