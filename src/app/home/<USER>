<ion-header>
  <ion-toolbar>
    <ion-title>{{user.first_name}} {{user.last_name}}</ion-title>
    <ion-buttons slot="end">
      <ion-button color="dark" (click)="modals.accounts = true" *ngIf="accountsService.getAccounts().length > 1">
        <ion-icon name="people-outline"></ion-icon>
      </ion-button>
      <ion-button color="dark" (click)="callPage('settings')">
        <ion-icon name="cog-outline"></ion-icon>
      </ion-button>
      <ion-button color="dark" (click)="callPage('nav-order')">
        <ion-icon name="list-outline"></ion-icon>
      </ion-button>
      <ion-button color="dark" (click)="logOut()">
        <ion-icon name="log-out-outline"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content>

  <div class="mx-1" *ngIf="user" >
    <ion-grid>
      <ion-row class="ion-align-items-center" >
        <ion-col size="8" ><img [src]="permissionValue('logo')" class="w-100" alt="Logo"></ion-col>
        <ion-col size="4" ><img [src]="logo" class="w-100" alt="Logo Tessa"></ion-col>
      </ion-row>
    </ion-grid>
  </div>

    <ion-card *ngFor="let item of renderList; let i = index" (click)="callPage(item)" [ngStyle]="{'background-color': permissionValue('color_' + berekenKleur(i))}">
        <ion-card-header>
            <ion-card-title class="text-white">
                {{ item.name }}
            </ion-card-title>
        </ion-card-header>
    </ion-card>

  <!--Modals-->
  <div class="modal-container" *ngIf="modals.accounts" (click)="modals.accounts = false;" >
    <ion-card class="modal-select-bottom" (click)="$event.stopPropagation()" >
      <ion-item lines="none" color="none" >
        <ion-label>Selecteer Account</ion-label>
      </ion-item>
      <div class="ion-padding overflow-auto mh-25-vh">
        <div *ngFor="let account of accountsService.getAccounts()">
          <div class="d-flex" >
            <ion-button (click)="accountsService.select(account); modals.accounts = false" class="w-100" fill="clear" >
              <div class="w-100 ion-text-left" [ngClass]="account.isLoggedIn() ? 'font-weight-semibold' : ''" >
                <div>{{account.subdomain}}</div>
                <div class="font-size-07" >{{account.first_name}} {{account.last_name}}</div>
              </div>
            </ion-button>
            <ion-button *ngIf="!account.isLoggedIn()" (click)="accountsService.remove(account.subdomain, account.user_id)" class="text-danger ml-auto" fill="clear" ><ion-icon name="close-outline"></ion-icon></ion-button>
          </div>
        </div>
      </div>
    </ion-card>
  </div>

</ion-content>
